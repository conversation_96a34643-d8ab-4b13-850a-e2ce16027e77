/* detail.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0;
}

/* 文章样式 */
.article {
  background: white;
  min-height: 100vh;
}

.article-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.article-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #999;
}

.article-source {
  color: #ff6b6b;
  font-weight: 500;
}

.article-image {
  width: 100%;
  height: 400rpx;
}

.article-content {
  padding: 30rpx;
}

.content-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
}

/* 分享区域 */
.share-section {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx;
  font-size: 28rpx;
}

.share-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

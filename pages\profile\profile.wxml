<!--profile.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 头部信息 -->
    <view class="header-section">
      <image src="{{starInfo.avatar}}" class="star-avatar" mode="aspectFill"></image>
      <view class="star-basic-info">
        <text class="star-name">{{starInfo.name}}</text>
        <text class="star-english-name">{{starInfo.englishName}}</text>
        <view class="occupation-tags">
          <text class="occupation-tag" wx:for="{{starInfo.occupation}}" wx:key="*this">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="card info-card">
      <view class="card-title">基本信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">生日</text>
          <text class="info-value">{{starInfo.birthday}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">出生地</text>
          <text class="info-value">{{starInfo.birthplace}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">身高</text>
          <text class="info-value">{{starInfo.height}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">体重</text>
          <text class="info-value">{{starInfo.weight}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">星座</text>
          <text class="info-value">{{starInfo.constellation}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">血型</text>
          <text class="info-value">{{starInfo.bloodType}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">经纪公司</text>
          <text class="info-value">{{starInfo.company}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">出道时间</text>
          <text class="info-value">{{starInfo.debut}}</text>
        </view>
      </view>
    </view>

    <!-- 个人简介 -->
    <view class="card">
      <view class="card-title">个人简介</view>
      <text class="introduction-text">{{starInfo.introduction}}</text>
    </view>

    <!-- 主要成就 -->
    <view class="card">
      <view class="card-title">主要成就</view>
      <view class="achievements-list">
        <view class="achievement-item" wx:for="{{starInfo.achievements}}" wx:key="*this">
          <text class="achievement-icon">🏆</text>
          <text class="achievement-text">{{item}}</text>
        </view>
      </view>
    </view>
  </view>
</scroll-view>

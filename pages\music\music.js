// music.js
const mockData = require('../../utils/mockData.js');

Page({
  data: {
    musicList: [],
    currentMusic: null,
    isPlaying: false,
    currentTime: 0,
    duration: 0
  },

  audioContext: null,

  onLoad() {
    this.loadMusicList();
    this.audioContext = wx.createInnerAudioContext();
    this.setupAudioEvents();
  },

  onUnload() {
    if (this.audioContext) {
      this.audioContext.destroy();
    }
  },

  loadMusicList() {
    this.setData({
      musicList: mockData.musicList
    });
  },

  setupAudioEvents() {
    this.audioContext.onPlay(() => {
      this.setData({ isPlaying: true });
    });

    this.audioContext.onPause(() => {
      this.setData({ isPlaying: false });
    });

    this.audioContext.onStop(() => {
      this.setData({ isPlaying: false });
    });

    this.audioContext.onTimeUpdate(() => {
      this.setData({
        currentTime: this.audioContext.currentTime,
        duration: this.audioContext.duration || 0
      });
    });

    this.audioContext.onEnded(() => {
      this.setData({ isPlaying: false });
      // 可以在这里实现自动播放下一首
    });

    this.audioContext.onError((res) => {
      console.error('音频播放错误:', res);
      wx.showToast({
        title: '播放失败',
        icon: 'none'
      });
    });
  },

  playMusic(e) {
    const index = e.currentTarget.dataset.index;
    const music = this.data.musicList[index];
    
    if (this.data.currentMusic && this.data.currentMusic.id === music.id) {
      // 如果点击的是当前播放的音乐，则切换播放/暂停状态
      this.togglePlay();
      return;
    }

    // 播放新音乐
    this.setData({
      currentMusic: music,
      currentTime: 0,
      duration: 0
    });

    this.audioContext.src = music.audioUrl;
    this.audioContext.play();

    wx.showToast({
      title: `正在播放: ${music.title}`,
      icon: 'none'
    });
  },

  togglePlay() {
    if (!this.data.currentMusic) return;

    if (this.data.isPlaying) {
      this.audioContext.pause();
    } else {
      this.audioContext.play();
    }
  },

  onProgressChange(e) {
    const value = e.detail.value;
    this.audioContext.seek(value);
  },

  onProgressChanging(e) {
    // 拖拽过程中的实时更新
    this.setData({
      currentTime: e.detail.value
    });
  },

  formatTime(time) {
    if (!time || isNaN(time)) return '00:00';
    
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
});

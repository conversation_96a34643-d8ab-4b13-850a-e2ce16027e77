// news.js
const mockData = require('../../utils/mockData.js');

Page({
  data: {
    newsList: [],
    hasMore: false,
    loading: false
  },

  onLoad() {
    this.loadNewsList();
  },

  loadNewsList() {
    // 模拟分页加载，这里直接加载所有数据
    this.setData({
      newsList: mockData.newsList,
      hasMore: false // 由于是模拟数据，设置为false
    });
  },

  viewNewsDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `./detail?id=${id}`
    });
  },

  loadMore() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    // 模拟加载更多数据
    setTimeout(() => {
      wx.showToast({
        title: '没有更多数据了',
        icon: 'none'
      });
      this.setData({
        loading: false,
        hasMore: false
      });
    }, 1000);
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.loadNewsList();
    wx.stopPullDownRefresh();
  }
});

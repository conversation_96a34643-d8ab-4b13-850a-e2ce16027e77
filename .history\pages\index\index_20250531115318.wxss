/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0;
}

/* 轮播图样式 */
.banner-swiper {
  height: 400rpx;
  margin-bottom: 20rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.banner-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.banner-desc {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/* 区块标题 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 20rpx 20rpx;
}

/* 导航网格 */
.nav-section {
  background: white;
  margin-bottom: 20rpx;
}

.nav-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.nav-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 12rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #666;
}

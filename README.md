# 明星资讯小程序

一个原生微信小程序，展示明星相关信息，包含音乐播放功能。

## 功能特性

### 📱 四个主要模块
1. **明星简介** - 详细的个人信息和成就展示
2. **音乐作品** - 音乐列表和播放器功能
3. **影视作品** - 分类筛选和详情展示
4. **娱乐新闻** - 新闻列表和详情页面

### 🎵 音乐播放器
- 支持播放/暂停控制
- 进度条拖拽
- 播放状态显示
- 音乐详情展示

### 🎬 影视作品
- 按类型筛选（电影/电视剧/网剧）
- 评分星级显示
- 详情弹窗展示

### 📰 娱乐新闻
- 新闻列表展示
- 详情页面
- 分享功能

## 项目结构

```
├── app.js                 # 应用入口
├── app.json              # 应用配置
├── app.wxss              # 全局样式
├── pages/                # 页面文件
│   ├── profile/          # 明星简介
│   ├── music/            # 音乐作品
│   ├── movies/           # 影视作品
│   └── news/             # 娱乐新闻
├── utils/
│   └── mockData.js       # 模拟数据
└── assets/               # 资源文件
    ├── images/           # 图片
    ├── audio/            # 音频
    └── icons/            # 图标
```

## 使用说明

1. 在微信开发者工具中打开项目
2. 替换 `assets/` 文件夹中的占位符资源
3. 根据需要修改 `utils/mockData.js` 中的数据
4. 编译运行

## 资源文件说明

### 需要替换的文件：
- `assets/icons/` - TabBar图标文件
- `assets/images/` - 图片文件（头像、封面、海报等）
- `assets/audio/` - 音频文件（音乐文件）

### 图标规格：
- 尺寸：81x81px
- 格式：PNG
- 需要普通和选中两种状态

### 音频格式支持：
- mp3, m4a, aac, wav

## 技术特点

- 原生微信小程序开发
- 响应式设计
- 模块化代码结构
- 完整的音乐播放功能
- 优雅的UI设计

## 注意事项

1. 音频文件路径需要与 `mockData.js` 中的 `audioUrl` 字段保持一致
2. 图片资源建议进行压缩优化
3. 可以将本地资源替换为网络资源URL

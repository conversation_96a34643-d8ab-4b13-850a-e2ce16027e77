/* music.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0;
}

/* 播放器控制栏 */
.player-bar {
  position: fixed;
  bottom: 100rpx; /* 为tabBar留出空间 */
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  padding: 20rpx;
  z-index: 100;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.player-cover {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.player-info {
  flex: 1;
}

.player-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.player-album {
  font-size: 22rpx;
  color: #999;
}

.player-controls {
  width: 80rpx;
  display: flex;
  justify-content: center;
}

.control-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-icon {
  font-size: 32rpx;
}

/* 音乐列表 */
.music-list {
  padding-bottom: 200rpx; /* 为底部播放器留出空间 */
}

.music-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.music-item.active {
  background: #fff5f5;
}

.music-item:last-child {
  border-bottom: none;
}

.music-cover {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}

.music-info {
  flex: 1;
}

.music-title {
  display: block;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.music-album {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.music-meta {
  display: flex;
  justify-content: space-between;
}

.music-duration,
.music-date {
  font-size: 22rpx;
  color: #999;
}

.music-actions {
  width: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.play-icon {
  font-size: 28rpx;
}

/* 进度条区域 */
.progress-section {
  position: fixed;
  bottom: 180rpx;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  border-top: 1rpx solid #e0e0e0;
  z-index: 99;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.current-time,
.total-time {
  font-size: 22rpx;
  color: #666;
}

.progress-slider {
  width: 100%;
}

/* 当前播放详情 */
.current-info {
  margin-bottom: 280rpx; /* 为底部控件留出空间 */
}

.current-music-detail {
  display: flex;
  padding: 24rpx;
}

.detail-cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
}

.detail-info {
  flex: 1;
}

.detail-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.detail-album,
.detail-lyricist,
.detail-composer,
.detail-date {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.5;
}

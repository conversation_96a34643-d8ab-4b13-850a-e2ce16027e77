<!--movies.wxml-->
<scroll-view class="scrollarea" scroll-y>
  <view class="container">
    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-item {{filterType === 'all' ? 'active' : ''}}" bindtap="filterMovies" data-type="all">
        全部
      </view>
      <view class="filter-item {{filterType === '电影' ? 'active' : ''}}" bindtap="filterMovies" data-type="电影">
        电影
      </view>
      <view class="filter-item {{filterType === '电视剧' ? 'active' : ''}}" bindtap="filterMovies" data-type="电视剧">
        电视剧
      </view>
      <view class="filter-item {{filterType === '网剧' ? 'active' : ''}}" bindtap="filterMovies" data-type="网剧">
        网剧
      </view>
    </view>

    <!-- 影视作品列表 -->
    <view class="movies-list">
      <view class="movie-item" wx:for="{{filteredMovies}}" wx:key="id" bindtap="viewMovieDetail" data-id="{{item.id}}">
        <image src="{{item.poster}}" class="movie-poster" mode="aspectFill"></image>
        <view class="movie-info">
          <text class="movie-title">{{item.title}}</text>
          <view class="movie-meta">
            <text class="movie-type">{{item.type}}</text>
            <text class="movie-role">{{item.role}}</text>
          </view>
          <text class="movie-genre">{{item.genre}}</text>
          <text class="movie-director">导演：{{item.director}}</text>
          <text class="movie-date">上映时间：{{item.releaseDate}}</text>
          <view class="movie-rating">
            <text class="rating-label">评分：</text>
            <view class="stars">
              <text class="star {{index < Math.floor(item.rating / 2) ? 'filled' : ''}}" 
                    wx:for="{{[1,2,3,4,5]}}" 
                    wx:key="*this" 
                    wx:for-index="index">★</text>
            </view>
            <text class="rating-score">{{item.rating}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredMovies.length === 0}}">
      <text class="empty-icon">🎬</text>
      <text class="empty-text">暂无{{filterType === 'all' ? '' : filterType}}作品</text>
    </view>
  </view>
</scroll-view>

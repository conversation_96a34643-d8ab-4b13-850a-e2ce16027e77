/* news.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0;
}

/* 新闻列表 */
.news-list {
  padding: 20rpx;
}

.news-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.news-item:active {
  transform: scale(0.98);
}

.news-image {
  width: 100%;
  height: 300rpx;
}

.news-content {
  padding: 24rpx;
}

.news-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.news-summary {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 22rpx;
  color: #999;
}

.news-source {
  color: #ff6b6b;
  font-weight: 500;
}

.news-time {
  flex: 1;
  text-align: center;
}

.news-stats {
  display: flex;
  align-items: center;
}

.read-count {
  display: flex;
  align-items: center;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.load-more-text {
  font-size: 28rpx;
  color: #666;
}

/* 没有更多 */
.no-more {
  text-align: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}

<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 轮播图 -->
    <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500">
      <swiper-item wx:for="{{bannerList}}" wx:key="id">
        <image src="{{item.image}}" class="banner-image" mode="aspectFill"></image>
        <view class="banner-content">
          <text class="banner-title">{{item.title}}</text>
          <text class="banner-desc">{{item.description}}</text>
        </view>
      </swiper-item>
    </swiper>

    <!-- 快捷导航 -->
    <view class="nav-section">
      <view class="section-title">快捷导航</view>
      <view class="nav-grid">
        <view class="nav-item" wx:for="{{navList}}" wx:key="id" bindtap="navigateTo" data-url="{{item.url}}">
          <image src="{{item.icon}}" class="nav-icon"></image>
          <text class="nav-text">{{item.title}}</text>
        </view>
      </view>
    </view>

    <!-- 最新动态 -->
    <view class="news-section">
      <view class="section-title">最新动态</view>
      <view class="news-list">
        <view class="news-item" wx:for="{{latestNews}}" wx:key="id" bindtap="navigateToNews" data-id="{{item.id}}">
          <image src="{{item.image}}" class="news-image"></image>
          <view class="news-content">
            <text class="news-title">{{item.title}}</text>
            <text class="news-time">{{item.publishTime}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 热门音乐 -->
    <view class="music-section">
      <view class="section-title">热门音乐</view>
      <view class="music-list">
        <view class="music-item" wx:for="{{hotMusic}}" wx:key="id" bindtap="playMusic" data-id="{{item.id}}">
          <image src="{{item.cover}}" class="music-cover"></image>
          <view class="music-info">
            <text class="music-title">{{item.title}}</text>
            <text class="music-album">{{item.album}}</text>
          </view>
          <view class="music-duration">{{item.duration}}</view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>

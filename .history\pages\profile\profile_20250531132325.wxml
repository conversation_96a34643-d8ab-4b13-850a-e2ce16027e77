<!--profile.wxml-->
<scroll-view class="scrollarea" scroll-y>
  <view class="container">
    <!-- 轮播图 -->
    <swiper class="banner-swiper" indicator-dots="true" autoplay="true" interval="3000" duration="500" circular="true">
      <swiper-item wx:for="{{bannerList}}" wx:key="id">
        <image src="{{item.image}}" class="banner-image" mode="aspectFill"></image>
        <view class="banner-overlay">
          <view class="banner-content">
            <text class="banner-title">{{item.title}}</text>
            <text class="banner-desc">{{item.description}}</text>
          </view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 头部信息 -->
    <view class="header-section">
      <view class="header-main">
        <image src="{{starInfo.avatar}}" class="star-avatar" mode="aspectFill"></image>
        <view class="star-basic-info">
          <text class="star-name">{{starInfo.name}}</text>
          <text class="star-english-name">{{starInfo.englishName}}</text>
          <view class="occupation-tags">
            <text class="occupation-tag" wx:for="{{starInfo.occupation}}" wx:key="*this">{{item}}</text>
          </view>
        </view>
      </view>
      <view class="star-stats">
        <view class="stat-item">
          <text class="stat-number">{{starInfo.musicCount || 15}}</text>
          <text class="stat-label">音乐作品</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{starInfo.movieCount || 8}}</text>
          <text class="stat-label">影视作品</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{starInfo.fansCount || '500W+'}}</text>
          <text class="stat-label">粉丝</text>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="card info-card">
      <view class="card-title">基本信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">生日</text>
          <text class="info-value">{{starInfo.birthday}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">出生地</text>
          <text class="info-value">{{starInfo.birthplace}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">身高</text>
          <text class="info-value">{{starInfo.height}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">体重</text>
          <text class="info-value">{{starInfo.weight}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">星座</text>
          <text class="info-value">{{starInfo.constellation}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">血型</text>
          <text class="info-value">{{starInfo.bloodType}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">经纪公司</text>
          <text class="info-value">{{starInfo.company}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">出道时间</text>
          <text class="info-value">{{starInfo.debut}}</text>
        </view>
      </view>
    </view>

    <!-- 个人简介 -->
    <view class="card">
      <view class="card-title">个人简介</view>
      <text class="introduction-text">{{starInfo.introduction}}</text>
    </view>

    <!-- 主要成就 -->
    <view class="card">
      <view class="card-title">主要成就</view>
      <view class="achievements-list">
        <view class="achievement-item" wx:for="{{starInfo.achievements}}" wx:key="*this">
          <text class="achievement-icon">🏆</text>
          <text class="achievement-text">{{item}}</text>
        </view>
      </view>
    </view>
  </view>
</scroll-view>

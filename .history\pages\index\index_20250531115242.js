// index.js
const mockData = require('../../utils/mockData.js');

Page({
  data: {
    bannerList: [
      {
        id: 1,
        title: '最新专辑发布',
        description: '《星空物语》即将上线',
        image: '/assets/images/banner-1.jpg'
      },
      {
        id: 2,
        title: '演唱会门票开售',
        description: '2024全国巡回演唱会',
        image: '/assets/images/banner-2.jpg'
      },
      {
        id: 3,
        title: '新电影上映',
        description: '《时光倒流》热映中',
        image: '/assets/images/banner-3.jpg'
      }
    ],
    navList: [
      {
        id: 1,
        title: '明星简介',
        icon: '/assets/icons/profile.png',
        url: '/pages/profile/profile'
      },
      {
        id: 2,
        title: '音乐作品',
        icon: '/assets/icons/music.png',
        url: '/pages/music/music'
      },
      {
        id: 3,
        title: '影视作品',
        icon: '/assets/icons/movie.png',
        url: '/pages/movies/movies'
      },
      {
        id: 4,
        title: '娱乐新闻',
        icon: '/assets/icons/news.png',
        url: '/pages/news/news'
      }
    ],
    latestNews: [],
    hotMusic: []
  },

  onLoad() {
    this.loadData();
  },

  loadData() {
    // 加载最新新闻（取前3条）
    const latestNews = mockData.newsList.slice(0, 3);

    // 加载热门音乐（取前3首）
    const hotMusic = mockData.musicList.slice(0, 3);

    this.setData({
      latestNews,
      hotMusic
    });
  },

  // 导航到指定页面
  navigateTo(e) {
    const url = e.currentTarget.dataset.url;
    wx.switchTab({
      url: url
    });
  },

  // 导航到新闻详情
  navigateToNews(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/news/detail?id=${id}`
    });
  },

  // 播放音乐
  playMusic(e) {
    const id = e.currentTarget.dataset.id;
    wx.switchTab({
      url: '/pages/music/music'
    });
    // 可以通过全局变量或事件传递要播放的音乐ID
    getApp().globalData.playMusicId = id;
  }
})

/* movies.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  background: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.filter-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #666;
  border-radius: 50rpx;
  transition: all 0.3s;
}

.filter-item.active {
  background: #ff6b6b;
  color: white;
}

/* 影视列表 */
.movies-list {
  padding: 20rpx;
}

.movie-item {
  display: flex;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.movie-item:active {
  transform: scale(0.98);
}

.movie-poster {
  width: 200rpx;
  height: 280rpx;
  flex-shrink: 0;
}

.movie-info {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
}

.movie-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.movie-meta {
  display: flex;
  margin-bottom: 12rpx;
}

.movie-type {
  background: #ff6b6b;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  margin-right: 12rpx;
}

.movie-role {
  background: #f0f0f0;
  color: #666;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.movie-genre,
.movie-director,
.movie-date {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.movie-rating {
  display: flex;
  align-items: center;
  margin-top: auto;
}

.rating-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.stars {
  display: flex;
  margin-right: 8rpx;
}

.star {
  font-size: 24rpx;
  color: #ddd;
  margin-right: 2rpx;
}

.star.filled {
  color: #ffd700;
}

.rating-score {
  font-size: 24rpx;
  color: #ff6b6b;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

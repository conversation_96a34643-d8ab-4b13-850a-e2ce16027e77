/* profile.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0;
}

/* 轮播图样式 */
.banner-swiper {
  height: 400rpx;
  position: relative;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent 40%, rgba(0, 0, 0, 0.7));
  display: flex;
  align-items: flex-end;
}

.banner-content {
  padding: 40rpx 30rpx 30rpx;
  color: white;
  width: 100%;
}

.banner-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.banner-desc {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* 头部区域 */
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  color: white;
  position: relative;
  overflow: hidden;
}

.header-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.header-main {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
  z-index: 1;
}

.star-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
}

.star-basic-info {
  flex: 1;
  position: relative;
  z-index: 1;
}

.star-name {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.star-english-name {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.occupation-tags {
  display: flex;
  flex-wrap: wrap;
}

.occupation-tag {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10rpx);
  padding: 10rpx 18rpx;
  border-radius: 25rpx;
  font-size: 22rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.star-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 22rpx;
  opacity: 0.8;
}

/* 信息卡片 */
.info-card {
  margin: 20rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.card {
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  overflow: hidden;
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #ff6b6b 0%, #ffa726 100%);
}

.card-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding: 30rpx 24rpx 0;
  position: relative;
}

.card-title::after {
  content: '';
  position: absolute;
  bottom: -12rpx;
  left: 24rpx;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}

.info-list {
  padding: 0 24rpx 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.3s;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item:hover {
  background-color: #f8f9fa;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 140rpx;
  position: relative;
}

.info-label::before {
  content: '•';
  color: #667eea;
  margin-right: 8rpx;
  font-weight: bold;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
  font-weight: 500;
}

/* 简介文本 */
.introduction-text {
  font-size: 28rpx;
  color: #555;
  line-height: 2;
  padding: 0 24rpx 30rpx;
  text-align: justify;
  text-indent: 2em;
}

/* 成就列表 */
.achievements-list {
  padding: 0 24rpx 30rpx;
}

.achievement-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  background: linear-gradient(90deg, #fff5f5 0%, #f0f8ff 100%);
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
  border-left: 6rpx solid #ffa726;
  transition: transform 0.3s, box-shadow 0.3s;
}

.achievement-item:hover {
  transform: translateX(8rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 167, 38, 0.2);
}

.achievement-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.achievement-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  font-weight: 500;
}

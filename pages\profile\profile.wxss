/* profile.wxss */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

/* 头部区域 */
.header-section {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  color: white;
}

.star-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.star-basic-info {
  flex: 1;
}

.star-name {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.star-english-name {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 20rpx;
}

.occupation-tags {
  display: flex;
  flex-wrap: wrap;
}

.occupation-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

/* 信息卡片 */
.info-card {
  margin-top: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding: 0 24rpx;
  padding-top: 24rpx;
}

.info-list {
  padding: 0 24rpx 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 140rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

/* 简介文本 */
.introduction-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  padding: 0 24rpx 24rpx;
}

/* 成就列表 */
.achievements-list {
  padding: 0 24rpx 24rpx;
}

.achievement-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.achievement-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.achievement-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

<!--news.wxml-->
<scroll-view class="scrollarea" scroll-y>
  <view class="container">
    <!-- 新闻列表 -->
    <view class="news-list">
      <view class="news-item" wx:for="{{newsList}}" wx:key="id" bindtap="viewNewsDetail" data-id="{{item.id}}">
        <image src="{{item.image}}" class="news-image" mode="aspectFill"></image>
        <view class="news-content">
          <text class="news-title">{{item.title}}</text>
          <text class="news-summary">{{item.summary}}</text>
          <view class="news-meta">
            <text class="news-source">{{item.source}}</text>
            <text class="news-time">{{item.publishTime}}</text>
            <view class="news-stats">
              <text class="read-count">👁 {{item.readCount}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" bindtap="loadMore" wx:if="{{hasMore}}">
      <text class="load-more-text">{{loading ? '加载中...' : '点击加载更多'}}</text>
    </view>

    <!-- 没有更多 -->
    <view class="no-more" wx:if="{{!hasMore && newsList.length > 0}}">
      <text class="no-more-text">没有更多新闻了</text>
    </view>
  </view>
</scroll-view>

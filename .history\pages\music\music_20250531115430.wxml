<!--music.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 播放器控制栏 -->
    <view class="player-bar" wx:if="{{currentMusic}}">
      <image src="{{currentMusic.cover}}" class="player-cover" mode="aspectFill"></image>
      <view class="player-info">
        <text class="player-title">{{currentMusic.title}}</text>
        <text class="player-album">{{currentMusic.album}}</text>
      </view>
      <view class="player-controls">
        <button class="control-btn" bindtap="togglePlay">
          <text class="control-icon">{{isPlaying ? '⏸️' : '▶️'}}</text>
        </button>
      </view>
    </view>

    <!-- 音乐列表 -->
    <view class="music-list">
      <view class="music-item {{currentMusic && currentMusic.id === item.id ? 'active' : ''}}" 
            wx:for="{{musicList}}" 
            wx:key="id" 
            bindtap="playMusic" 
            data-index="{{index}}">
        <image src="{{item.cover}}" class="music-cover" mode="aspectFill"></image>
        <view class="music-info">
          <text class="music-title">{{item.title}}</text>
          <text class="music-album">{{item.album}}</text>
          <view class="music-meta">
            <text class="music-duration">{{item.duration}}</text>
            <text class="music-date">{{item.releaseDate}}</text>
          </view>
        </view>
        <view class="music-actions">
          <text class="play-icon">{{currentMusic && currentMusic.id === item.id && isPlaying ? '🎵' : '🎶'}}</text>
        </view>
      </view>
    </view>

    <!-- 播放进度条 -->
    <view class="progress-section" wx:if="{{currentMusic}}">
      <view class="progress-info">
        <text class="current-time">{{formatTime(currentTime)}}</text>
        <text class="total-time">{{formatTime(duration)}}</text>
      </view>
      <slider 
        class="progress-slider"
        value="{{currentTime}}"
        max="{{duration}}"
        block-size="12"
        backgroundColor="#e0e0e0"
        activeColor="#ff6b6b"
        bindchange="onProgressChange"
        bindchanging="onProgressChanging"
      />
    </view>

    <!-- 当前播放信息 -->
    <view class="current-info" wx:if="{{currentMusic}}">
      <view class="card">
        <view class="card-title">正在播放</view>
        <view class="current-music-detail">
          <image src="{{currentMusic.cover}}" class="detail-cover" mode="aspectFill"></image>
          <view class="detail-info">
            <text class="detail-title">{{currentMusic.title}}</text>
            <text class="detail-album">专辑：{{currentMusic.album}}</text>
            <text class="detail-lyricist">作词：{{currentMusic.lyricist}}</text>
            <text class="detail-composer">作曲：{{currentMusic.composer}}</text>
            <text class="detail-date">发行时间：{{currentMusic.releaseDate}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</scroll-view>

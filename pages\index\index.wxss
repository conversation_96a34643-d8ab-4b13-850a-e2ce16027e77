/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 0;
}

/* 轮播图样式 */
.banner-swiper {
  height: 400rpx;
  margin-bottom: 20rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.banner-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.banner-desc {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/* 区块标题 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 30rpx 20rpx 20rpx;
}

/* 导航网格 */
.nav-section {
  background: white;
  margin-bottom: 20rpx;
}

.nav-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.nav-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 12rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #666;
}

/* 新闻列表 */
.news-section {
  background: white;
  margin-bottom: 20rpx;
}

.news-list {
  padding: 0 20rpx 20rpx;
}

.news-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.news-item:last-child {
  border-bottom: none;
}

.news-image {
  width: 120rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.news-content {
  flex: 1;
}

.news-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.news-time {
  font-size: 22rpx;
  color: #999;
}

/* 音乐列表 */
.music-section {
  background: white;
}

.music-list {
  padding: 0 20rpx 20rpx;
}

.music-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.music-item:last-child {
  border-bottom: none;
}

.music-cover {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.music-info {
  flex: 1;
}

.music-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.music-album {
  font-size: 22rpx;
  color: #999;
}

.music-duration {
  font-size: 22rpx;
  color: #999;
}

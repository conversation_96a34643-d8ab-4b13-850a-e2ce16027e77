// movies.js
const mockData = require('../../utils/mockData.js');

Page({
  data: {
    moviesList: [],
    filteredMovies: [],
    filterType: 'all'
  },

  onLoad() {
    this.loadMoviesList();
  },

  loadMoviesList() {
    const moviesList = mockData.moviesList;
    this.setData({
      moviesList,
      filteredMovies: moviesList
    });
  },

  filterMovies(e) {
    const type = e.currentTarget.dataset.type;
    let filteredMovies = [];

    if (type === 'all') {
      filteredMovies = this.data.moviesList;
    } else {
      filteredMovies = this.data.moviesList.filter(movie => movie.type === type);
    }

    this.setData({
      filterType: type,
      filteredMovies
    });
  },

  viewMovieDetail(e) {
    const id = e.currentTarget.dataset.id;
    const movie = this.data.moviesList.find(item => item.id === id);
    
    if (movie) {
      wx.showModal({
        title: movie.title,
        content: `类型：${movie.type}\n角色：${movie.role}\n导演：${movie.director}\n类型：${movie.genre}\n上映时间：${movie.releaseDate}\n评分：${movie.rating}\n\n简介：${movie.description}`,
        showCancel: false,
        confirmText: '知道了'
      });
    }
  }
});

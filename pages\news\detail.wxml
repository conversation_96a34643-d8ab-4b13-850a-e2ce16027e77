<!--detail.wxml-->
<scroll-view class="scrollarea" scroll-y>
  <view class="container">
    <view class="article" wx:if="{{newsDetail}}">
      <!-- 文章头部 -->
      <view class="article-header">
        <text class="article-title">{{newsDetail.title}}</text>
        <view class="article-meta">
          <text class="article-source">{{newsDetail.source}}</text>
          <text class="article-time">{{newsDetail.publishTime}}</text>
          <text class="article-reads">阅读 {{newsDetail.readCount}}</text>
        </view>
      </view>

      <!-- 文章图片 -->
      <image src="{{newsDetail.image}}" class="article-image" mode="aspectFill" wx:if="{{newsDetail.image}}"></image>

      <!-- 文章内容 -->
      <view class="article-content">
        <text class="content-text">{{newsDetail.content}}</text>
      </view>

      <!-- 分享按钮 -->
      <view class="share-section">
        <button class="share-btn" open-type="share">
          <text class="share-icon">📤</text>
          <text class="share-text">分享给朋友</text>
        </button>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:else>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</scroll-view>

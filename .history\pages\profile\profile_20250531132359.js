// profile.js
const mockData = require('../../utils/mockData.js');

Page({
  data: {
    starInfo: {},
    bannerList: [
      {
        id: 1,
        title: '最新专辑《星空物语》',
        description: '全新音乐体验，即将震撼发布',
        image: 'https://picsum.photos/750/400?random=1'
      },
      {
        id: 2,
        title: '2024全国巡回演唱会',
        description: '与你共度美好音乐时光',
        image: 'https://picsum.photos/750/400?random=2'
      },
      {
        id: 3,
        title: '新电影《时光倒流》',
        description: '突破自我，全新演技挑战',
        image: 'https://picsum.photos/750/400?random=3'
      }
    ]
  },

  onLoad() {
    this.loadStarInfo();
  },

  loadStarInfo() {
    this.setData({
      starInfo: mockData.starInfo
    });
  }
});

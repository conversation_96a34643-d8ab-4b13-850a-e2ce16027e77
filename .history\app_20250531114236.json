{"pages": ["pages/index/index", "pages/profile/profile", "pages/music/music", "pages/movies/movies", "pages/news/news"], "window": {"navigationBarTextStyle": "white", "navigationBarTitleText": "明星资讯", "navigationBarBackgroundColor": "#1a1a1a"}, "tabBar": {"color": "#999999", "selectedColor": "#ff6b6b", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "assets/icons/home.png", "selectedIconPath": "assets/icons/home-active.png"}, {"pagePath": "pages/profile/profile", "text": "明星简介", "iconPath": "assets/icons/profile.png", "selectedIconPath": "assets/icons/profile-active.png"}, {"pagePath": "pages/music/music", "text": "音乐作品", "iconPath": "assets/icons/music.png", "selectedIconPath": "assets/icons/music-active.png"}, {"pagePath": "pages/movies/movies", "text": "影视作品", "iconPath": "assets/icons/movie.png", "selectedIconPath": "assets/icons/movie-active.png"}, {"pagePath": "pages/news/news", "text": "娱乐新闻", "iconPath": "assets/icons/news.png", "selectedIconPath": "assets/icons/news-active.png"}]}, "style": "v2", "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}
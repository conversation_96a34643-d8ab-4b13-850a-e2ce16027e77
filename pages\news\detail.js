// detail.js
const mockData = require('../../utils/mockData.js');

Page({
  data: {
    newsDetail: null,
    newsId: null
  },

  onLoad(options) {
    const id = parseInt(options.id);
    this.setData({ newsId: id });
    this.loadNewsDetail(id);
  },

  loadNewsDetail(id) {
    const newsDetail = mockData.newsList.find(news => news.id === id);
    
    if (newsDetail) {
      this.setData({ newsDetail });
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: newsDetail.title.length > 10 ? newsDetail.title.substring(0, 10) + '...' : newsDetail.title
      });
    } else {
      wx.showToast({
        title: '新闻不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onShareAppMessage() {
    const { newsDetail } = this.data;
    return {
      title: newsDetail ? newsDetail.title : '娱乐新闻',
      path: `/pages/news/detail?id=${this.data.newsId}`,
      imageUrl: newsDetail ? newsDetail.image : ''
    };
  }
});
